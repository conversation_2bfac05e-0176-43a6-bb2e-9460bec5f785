// AiResponseRenderer.tsx
import React, {
  FC,
  useRef,
  useState,
  useEffect,
  ReactNode,
  useMemo,
  useCallback
} from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import { BlockMath, InlineMath } from 'react-katex';
import mermaid from 'mermaid';
import type { Components } from 'react-markdown';

// Emoji 处理工具函数
const processEmojis = (text: string): string => {
  // 只处理基础的文本表情符号，不处理中文词汇
  const emojiMap: { [key: string]: string } = {
    // 基础表情符号
    '☺': '☺️',
    ':)': '😊',
    ':-)': '😊',
    ':(': '😢',
    ':-(': '😢',
    ':D': '😃',
    ':-D': '😃',
    ';)': '😉',
    ';-)': '😉',
    ':P': '😛',
    ':-P': '😛',
    ':p': '😛',
    ':-p': '😛',
    ':o': '😮',
    ':-o': '😮',
    ':O': '😱',
    ':-O': '😱',
    ':|': '😐',
    ':-|': '😐',
    ':*': '😘',
    ':-*': '😘',
    '<3': '❤️',
    '</3': '💔',

    // 只处理特定的符号转换，不处理中文词汇
    '~': '～',
    '。。。': '…',
    '...': '…'
  };

  let processedText = text;

  // 只处理表情符号，使用精确匹配
  Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
    // 对于特殊字符，需要转义
    const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 对于表情符号，使用更简单和安全的匹配方式
    if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
      // 表情符号需要边界匹配，避免在单词中间替换
      // 使用简单的边界检查，避免复杂的正则表达式
      const parts = processedText.split(textEmoji);
      if (parts.length > 1) {
        processedText = parts.join(emoji);
      }
    } else if (textEmoji === '~') {
      // 只在句末或独立使用时替换波浪号
      processedText = processedText.replace(/~(?=\s|$)/g, emoji);
    } else if (textEmoji === '。。。' || textEmoji === '...') {
      // 省略号直接替换
      const regex = new RegExp(escapedText, 'g');
      processedText = processedText.replace(regex, emoji);
    }
  });

  return processedText;
};

// Mermaid初始化状态
let mermaidInitialized = false;

// Mermaid图表组件
interface MermaidDiagramProps {
  code: string;
}

const MermaidDiagram: FC<MermaidDiagramProps> = ({ code }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [svg, setSvg] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 确保Mermaid只初始化一次
    if (!mermaidInitialized) {
      try {
        mermaid.initialize({ 
          startOnLoad: false, 
          theme: 'dark',
          securityLevel: 'loose',
          fontFamily: 'inherit'
        });
        mermaidInitialized = true;
      } catch (initError) {
        console.error("Mermaid initialization error:", initError);
        setError("Failed to initialize Mermaid");
        return;
      }
    }

    // 清除之前的内容
    setSvg(null);
    setError(null);
    
    const container = containerRef.current;
    if (!container) return;

    const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`;
    container.id = id;

    // 异步渲染图表
    const renderDiagram = async () => {
      try {
        const { svg } = await mermaid.render(id, code);
        setSvg(svg);
      } catch (renderError:any) {
        console.error("Mermaid render error:", renderError);
        setError(renderError.message);
      }
    };

    renderDiagram();
  }, [code]);

  if (error) {
    return (
      <div className="mermaid-error">
        <p>Failed to render diagram:</p>
        <pre>{error}</pre>
        <details>
          <summary>View source</summary>
          <pre>{code}</pre>
        </details>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="mermaid-container">
      {!svg && <div className="mermaid-loading">Rendering diagram...</div>}
      {svg && <div dangerouslySetInnerHTML={{ __html: svg }} />}
    </div>
  );
};

// 错误边界组件
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    console.error("Component render error:", error, info);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <div className="render-error">Component render failed</div>;
    }
    return this.props.children;
  }
}

// 主渲染器组件
interface AiResponseRendererProps {
  content: string;
  fontSize?: 'sm' | 'base' | 'lg'; // 字体大小选项
  density?: 'compact' | 'normal' | 'comfortable'; // 新增：内容密度选项
}

// 字体优化工具函数
const getOptimizedFontClass = (fontSize: string = 'base', density: string = 'normal') => {
  // 基础字体栈 - 中英文混合优化
  const baseFontStack = `
    -apple-system, BlinkMacSystemFont, 
    "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", 
    "Fira Sans", "Droid Sans", "Helvetica Neue", 
    "Microsoft YaHei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", 
    "Source Han Sans CN", "Noto Sans CJK SC", "WenQuanYi Micro Hei", 
    sans-serif
  `.replace(/\s+/g, ' ').trim();

  // 代码字体栈
  const codeFontStack = `
    "Fira Code", "JetBrains Mono", "Source Code Pro", 
    "SF Mono", Monaco, Inconsolata, "Liberation Mono", 
    "Noto Sans Mono CJK SC", "Source Han Sans CN", 
    "Microsoft YaHei", Consolas, "Courier New", monospace
  `.replace(/\s+/g, ' ').trim();

  return {
    baseFontStack,
    codeFontStack,
    // 字体大小映射
    fontSize: {
      sm: {
        base: '0.875rem',      // 14px
        small: '0.75rem',      // 12px
        h1: '1.5rem',          // 24px
        h2: '1.375rem',        // 22px
        h3: '1.25rem',         // 20px
        h4: '1.125rem',        // 18px
        h5: '1rem',            // 16px
        h6: '0.875rem',        // 14px
        code: '0.8125rem',     // 13px
      },
      base: {
        base: '1rem',          // 16px
        small: '0.875rem',     // 14px
        h1: '1.75rem',         // 28px
        h2: '1.5rem',          // 24px
        h3: '1.375rem',        // 22px
        h4: '1.25rem',         // 20px
        h5: '1.125rem',        // 18px
        h6: '1rem',            // 16px
        code: '0.9375rem',     // 15px
      },
      lg: {
        base: '1.125rem',      // 18px
        small: '1rem',         // 16px
        h1: '2rem',            // 32px
        h2: '1.75rem',         // 28px
        h3: '1.5rem',          // 24px
        h4: '1.375rem',        // 22px
        h5: '1.25rem',         // 20px
        h6: '1.125rem',        // 18px
        code: '1.0625rem',     // 17px
      }
    }[fontSize],
    // 行高映射
    lineHeight: {
      compact: '1.4',
      normal: '1.6', 
      comfortable: '1.8'
    }[density],
    // 段落间距映射
    spacing: {
      compact: {
        paragraph: '0.5rem',
        heading: '1rem',
        list: '0.25rem'
      },
      normal: {
        paragraph: '0.75rem',
        heading: '1.5rem', 
        list: '0.375rem'
      },
      comfortable: {
        paragraph: '1rem',
        heading: '2rem',
        list: '0.5rem'
      }
    }[density]
  };
};

const AiResponseRenderer: FC<AiResponseRendererProps> = ({ 
  content, 
  fontSize = 'base', 
  density = 'normal' 
}) => {
  // 清理内容中的多余空白和换行 - 特别针对WorkflowNodeItem的whitespace-pre-wrap问题
  const cleanContent = (text: string): string => {
    // 首先进行基础清理
    let cleaned = text.trim()

    // 特别处理表格前的大量空行 - 使用更激进的清理策略
    cleaned = cleaned.replace(/(\n\s*){100,}(\|)/g, '\n\n$2') // 100+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){50,}(\|)/g, '\n\n$2')  // 50+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){30,}(\|)/g, '\n\n$2')  // 30+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){20,}(\|)/g, '\n\n$2')  // 20+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){10,}(\|)/g, '\n\n$2')  // 10+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){5,}(\|)/g, '\n\n$2')   // 5+空行 → 2个换行
    cleaned = cleaned.replace(/(\n\s*){3,}(\|)/g, '\n\n$2')   // 3+空行 → 2个换行

    // 处理任何形式的大量空白字符
    cleaned = cleaned.replace(/\s{20,}/g, ' ')  // 将20+连续空格替换为单个空格
    cleaned = cleaned.replace(/\s{10,}/g, ' ')  // 将10+连续空格替换为单个空格

    // 然后进行常规清理
    cleaned = cleaned
      .replace(/\n{5,}/g, '\n\n')     // 将5个或更多连续换行替换为2个换行
      .replace(/\n{3,}/g, '\n\n')     // 将3个或更多连续换行替换为2个换行
      .replace(/^\s*\n/gm, '\n')      // 去除行首的空白字符
      .replace(/\n\s*$/gm, '\n')      // 去除行尾的空白字符
      .replace(/\n\s+\n/g, '\n\n')    // 去除换行之间的空白字符
      .replace(/(\|.*\|)\n+/g, '$1\n') // 确保表格后只有一个换行

    // 最后的安全检查：确保表格前没有超过2个换行
    cleaned = cleaned.replace(/\n{2,}(\|)/g, '\n\n$1')

    return cleaned
  }

  // 移除复杂的状态管理，直接渲染内容以避免闪烁
  // const [visibleContent, setVisibleContent] = useState('');
  // const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // useEffect(() => {
  //   // 初始渲染部分内容（前5000字符）
  //   setVisibleContent(content.slice(0, 5000));
  //
  //   // 如果内容较长，延迟渲染剩余部分
  //   if (content.length > 5000) {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //
  //     timeoutRef.current = setTimeout(() => {
  //       setVisibleContent(content);
  //     }, 300);
  //   } else {
  //     setVisibleContent(content);
  //   }

  //   return () => {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //   };
  // }, [content]);

  // 处理文本中的LaTeX公式
  const processLatex = useCallback((text: string): ReactNode[] => {
    // 处理块级公式 $$...$$ 和内联公式 $...$
    const parts = text.split(/(\$\$.*?\$\$|\$.*?\$)/g);
    return parts.map((part, index) => {
      if (part.startsWith('$$') && part.endsWith('$$')) {
        try {
          return <BlockMath key={index} math={part.slice(2, -2).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      } else if (part.startsWith('$') && part.endsWith('$')) {
        try {
          return <InlineMath key={index} math={part.slice(1, -1).trim()} />;
        } catch (error) {
          return (
            <span key={index} className="latex-error">
              {part}
            </span>
          );
        }
      }
      return part;
    });
  }, []);

  // 使用useMemo优化渲染器配置
  const renderers = useMemo<Components>(() => {
    const fontConfig = getOptimizedFontClass(fontSize, density);
    
    return {
      // 段落渲染器 - 支持 Emoji 和 LaTeX，优化字体
      p: ({ children }) => {
        const processedChildren = React.Children.map(children, child => {
          if (typeof child === 'string') {
            // 先处理 emoji，再处理 LaTeX
            const emojiProcessed = processEmojis(child);
            return processLatex(emojiProcessed);
          }
          return child;
        });

        return (
          <p 
            className="my-2 md:my-1 leading-relaxed"
            style={{
              fontSize: fontConfig.fontSize.base,
              lineHeight: fontConfig.lineHeight,
              fontFamily: fontConfig.baseFontStack,
              marginBottom: fontConfig.spacing.paragraph,
              marginTop: fontConfig.spacing.paragraph,
              // 中文字体优化
              fontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
              textRendering: 'optimizeLegibility',
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale',
              // 中英文混排优化
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              hyphens: 'auto'
            }}
          >
            {processedChildren}
          </p>
        );
      },

      // 标题渲染器 - 优化字体和间距
      h1: ({ children }) => (
        <h1 
          className="font-bold mt-6 md:mt-3 mb-4"
          style={{
            fontSize: fontConfig.fontSize.h1,
            lineHeight: '1.3',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 700,
            letterSpacing: '-0.025em'
          }}
        >
          {children}
        </h1>
      ),
      h2: ({ children }) => (
        <h2 
          className="font-semibold mt-5 md:mt-2.5 mb-3"
          style={{
            fontSize: fontConfig.fontSize.h2,
            lineHeight: '1.35',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading * 0.8,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 600,
            letterSpacing: '-0.025em'
          }}
        >
          {children}
        </h2>
      ),
      h3: ({ children }) => (
        <h3 
          className="font-medium mt-4 md:mt-2 mb-2"
          style={{
            fontSize: fontConfig.fontSize.h3,
            lineHeight: '1.4',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading * 0.6,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 500
          }}
        >
          {children}
        </h3>
      ),
      h4: ({ children }) => (
        <h4 
          className="font-medium mt-4 md:mt-2 mb-2"
          style={{
            fontSize: fontConfig.fontSize.h4,
            lineHeight: '1.4',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading * 0.5,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 500
          }}
        >
          {children}
        </h4>
      ),
      h5: ({ children }) => (
        <h5 
          className="font-medium mt-3 md:mt-1.5 mb-2"
          style={{
            fontSize: fontConfig.fontSize.h5,
            lineHeight: '1.45',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading * 0.4,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 500
          }}
        >
          {children}
        </h5>
      ),
      h6: ({ children }) => (
        <h6 
          className="font-medium mt-3 md:mt-1.5 mb-2"
          style={{
            fontSize: fontConfig.fontSize.h6,
            lineHeight: '1.45',
            fontFamily: fontConfig.baseFontStack,
            marginTop: fontConfig.spacing.heading * 0.3,
            marginBottom: fontConfig.spacing.paragraph,
            fontWeight: 500
          }}
        >
          {children}
        </h6>
      ),

      // 文本渲染器 - 支持 Emoji 和 LaTeX，优化字体
      text: ({ children }) => {
        const textContent = String(children);
        // 先处理 emoji，再处理 LaTeX
        const emojiProcessed = processEmojis(textContent);
        const processedContent = processLatex(emojiProcessed);
        return (
          <span 
            style={{
              fontSize: fontConfig.fontSize.base,
              fontFamily: fontConfig.baseFontStack,
              lineHeight: fontConfig.lineHeight
            }}
          >
            {processedContent}
          </span>
        );
      },

      // 列表渲染器 - 优化间距和字体
      ul: ({ children }) => (
        <ul 
          className="list-disc list-inside"
          style={{
            fontSize: fontConfig.fontSize.base,
            lineHeight: fontConfig.lineHeight,
            fontFamily: fontConfig.baseFontStack,
            marginBottom: fontConfig.spacing.paragraph,
            paddingLeft: '1.5rem'
          }}
        >
          {children}
        </ul>
      ),
      ol: ({ children }) => (
        <ol 
          className="list-decimal list-inside"
          style={{
            fontSize: fontConfig.fontSize.base,
            lineHeight: fontConfig.lineHeight,
            fontFamily: fontConfig.baseFontStack,
            marginBottom: fontConfig.spacing.paragraph,
            paddingLeft: '1.5rem'
          }}
        >
          {children}
        </ol>
      ),
      li: ({ children }) => (
        <li 
          style={{
            marginBottom: fontConfig.spacing.list,
            lineHeight: fontConfig.lineHeight
          }}
        >
          {children}
        </li>
      ),

      // 强调文本渲染器
      strong: ({ children }) => (
        <strong 
          className="font-semibold"
          style={{
            fontFamily: fontConfig.baseFontStack,
            fontWeight: 600
          }}
        >
          {children}
        </strong>
      ),
      em: ({ children }) => (
        <em 
          className="italic"
          style={{
            fontFamily: fontConfig.baseFontStack,
            fontStyle: 'italic'
          }}
        >
          {children}
        </em>
      ),

      // 代码块渲染器 - 优化字体
      code: ({ children, className, ref, ...props }) => {
        const codeContent = String(children).trim();
        const match = /language-(\w+)/.exec(className || '');
        const language = match?.[1];
        const inline = (props as any)?.inline;
        
        // Mermaid图表处理
        if (language === 'mermaid') {
          return (
            <ErrorBoundary fallback={
              <div className="mermaid-error">
                <pre>Failed to render Mermaid diagram</pre>
                <details>
                  <summary>View source</summary>
                  <pre>{codeContent}</pre>
                </details>
              </div>
            }>
              <MermaidDiagram code={codeContent} />
            </ErrorBoundary>
          );
        }

        // LaTeX块处理
        if (language === 'latex' || language === 'tex') {
          return (
            <ErrorBoundary fallback={
              <div className="latex-error-block">
                <SyntaxHighlighter language="text" style={vscDarkPlus as any}>
                  {`LaTeX syntax error in block:\n\n${codeContent}`}
                </SyntaxHighlighter>
              </div>
            }>
              <BlockMath math={codeContent} />
            </ErrorBoundary>
          );
        }

        // 内联代码处理
        if (inline) {
          return (
            <code 
              className="inline-code px-1.5 py-0.5 bg-gray-100 rounded text-sm"
              ref={ref} 
              style={{
                fontSize: fontConfig.fontSize.code,
                fontFamily: fontConfig.codeFontStack,
                backgroundColor: 'rgba(229, 231, 235, 0.8)',
                padding: '0.125rem 0.375rem',
                borderRadius: '0.25rem',
                color: '#1f2937'
              }}
              {...props}
            >
              {children}
            </code>
          );
        }

        // 语法高亮处理
        return (
          <div 
            className="code-block-wrapper"
            style={{ 
              fontSize: fontConfig.fontSize.code,
              fontFamily: fontConfig.codeFontStack,
              marginBottom: fontConfig.spacing.paragraph
            }}
          >
            <SyntaxHighlighter
              style={vscDarkPlus as any}
              language={language || 'text'}
              PreTag="div"
              showLineNumbers={!inline && (language || '').length > 0}
              wrapLongLines
              customStyle={{
                fontSize: fontConfig.fontSize.code,
                fontFamily: fontConfig.codeFontStack,
                lineHeight: '1.5',
                borderRadius: '0.5rem',
                margin: 0
              }}
              {...props}
            >
              {codeContent}
            </SyntaxHighlighter>
          </div>
        );
      },

      // 引用块渲染器
      blockquote: ({ children }) => (
        <blockquote 
          className="border-l-4 border-gray-300 pl-4 italic"
          style={{
            fontSize: fontConfig.fontSize.base,
            lineHeight: fontConfig.lineHeight,
            fontFamily: fontConfig.baseFontStack,
            marginBottom: fontConfig.spacing.paragraph,
            color: '#6b7280',
            borderLeftColor: '#d1d5db',
            paddingLeft: '1rem'
          }}
        >
          {children}
        </blockquote>
      ),

      // 表格渲染器 - 支持横向滚动和字体优化
      table: ({ children }) => (
        <div 
          className="table-container overflow-x-auto mb-4"
          style={{ 
            marginBottom: fontConfig.spacing.paragraph,
            borderRadius: '0.5rem',
            border: '1px solid #e5e7eb'
          }}
        >
          <table 
            className="min-w-full"
            style={{
              fontSize: fontConfig.fontSize.small,
              fontFamily: fontConfig.baseFontStack,
              lineHeight: '1.5'
            }}
          >
            {children}
          </table>
        </div>
      ),

      // 表格单元格渲染器
      th: ({ children }) => (
        <th 
          className="px-4 py-2 bg-gray-50 font-semibold text-left border-b border-gray-200"
          style={{
            fontSize: fontConfig.fontSize.small,
            fontFamily: fontConfig.baseFontStack,
            fontWeight: 600
          }}
        >
          {children}
        </th>
      ),
      td: ({ children }) => (
        <td 
          className="px-4 py-2 border-b border-gray-100"
          style={{
            fontSize: fontConfig.fontSize.small,
            fontFamily: fontConfig.baseFontStack
          }}
        >
          {children}
        </td>
      ),

      // 链接渲染器
      a: ({ children, href, ...props }) => (
        <a 
          href={href}
          className="text-blue-600 hover:text-blue-800 underline"
          style={{
            fontFamily: fontConfig.baseFontStack,
            textDecorationColor: 'rgba(37, 99, 235, 0.4)'
          }}
          {...props}
        >
          {children}
        </a>
      ),

      // 图片渲染器 - 优化显示
      img: ({ src, alt, ...props }) => (
        <div 
          className="image-container my-4"
          style={{ marginBottom: fontConfig.spacing.paragraph }}
        >
          <img
            src={src}
            alt={alt || 'Image'}
            loading="lazy"
            className="max-w-full h-auto rounded-lg shadow-sm"
            style={{ borderRadius: '0.5rem' }}
            {...props}
          />
          {alt && (
            <div 
              className="image-caption text-center text-gray-500 mt-2"
              style={{
                fontSize: fontConfig.fontSize.small,
                fontFamily: fontConfig.baseFontStack,
                lineHeight: '1.4'
              }}
            >
              {alt}
            </div>
          )}
        </div>
      ),

      // HR分割线渲染器
      thematicBreak: () => (
        <hr 
          className="ai-response-hr border-gray-200"
          style={{ 
            marginTop: fontConfig.spacing.paragraph,
            marginBottom: fontConfig.spacing.paragraph,
            borderColor: '#e5e7eb'
          }} 
        />
      )
    };
  }, [processLatex, fontSize, density]);

  // 获取优化的字体配置
  const fontConfig = getOptimizedFontClass(fontSize, density);

  return (
    <div 
      className="ai-response-container max-w-4xl"
      style={{
        fontFamily: fontConfig.baseFontStack,
        fontSize: fontConfig.fontSize.base,
        lineHeight: fontConfig.lineHeight,
        // 字体渲染优化
        fontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
        textRendering: 'optimizeLegibility',
        WebkitFontSmoothing: 'antialiased',
        MozOsxFontSmoothing: 'grayscale',
        // 中英文混排优化
        wordBreak: 'break-word',
        overflowWrap: 'break-word',
        // 响应式字体缩放
        '@media (max-width: 768px)': {
          fontSize: `calc(${fontConfig.fontSize.base} * 0.9)`
        }
      }}
    >
      <ErrorBoundary fallback={
        <div 
          className="render-error p-4 bg-red-50 border border-red-200 rounded-lg"
          style={{
            fontFamily: fontConfig.baseFontStack,
            fontSize: fontConfig.fontSize.small,
            color: '#dc2626'
          }}
        >
          内容渲染失败。响应可能包含不支持的格式。
        </div>
      }>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={renderers}
          skipHtml={false}
        >
          {cleanContent(content)}
        </ReactMarkdown>
      </ErrorBoundary>
    </div>
  );
};

export default React.memo(AiResponseRenderer);